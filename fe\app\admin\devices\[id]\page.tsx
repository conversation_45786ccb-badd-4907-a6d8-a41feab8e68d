'use client'

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const channels = Array.from({length: 16}).fill(0).map((_, i) =>  {
  return {
    sn: `SN00${i}`,
    ai: "Text",
    sampleMode: "Mode1",
    threshold: "10",
    power: "5W",
    kangdoudongyuzhi: 100
    }})

export default function Page() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-row gap-4 justify-between items-center h-full">
        <div>设备 - BTT_BOX_01</div>
        <Button className='cursor-pointer rounded-none'>新建场景</Button>
      </div>

      <div className='flex flex-row gap-2 items-center'>
        <Button className='cursor-pointer rounded-none'>启动测试</Button>
        <Button className='cursor-pointer rounded-none'>停止测试</Button>
        <Button className='cursor-pointer rounded-none'>打开激光</Button>
        <Button className='cursor-pointer rounded-none'>关闭激光</Button>
        <Button className='cursor-pointer rounded-none'>批量编辑</Button>
        <Button className='cursor-pointer rounded-none'>保存参数</Button>
      </div>
      <div className="grid grid-cols-5 md:grid-cols-5 lg:grid-cols-5 sm:grid-cols-2 gap-8">
        <div className="bg-background border rounded p-4 col-span-4">
          <h2>板卡通道</h2>
          <Table>
            <TableCaption></TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">SN</TableHead>
                <TableHead>AI</TableHead>
                <TableHead>采样模式</TableHead>
                <TableHead>触发阈值</TableHead>
                <TableHead>激光功率</TableHead>
                <TableHead>抗抖动阈值</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {channels.map((invoice) => (
                <TableRow key={invoice.sn}>
                  <TableCell className="font-semibold">{invoice.sn}</TableCell>
                  <TableCell className="font-semibold">{invoice.ai}</TableCell>
                  <TableCell>{invoice.sampleMode}</TableCell>
                  <TableCell>{invoice.threshold}</TableCell>
                  <TableCell>{invoice.power}</TableCell>
                  <TableCell>{invoice.kangdoudongyuzhi}</TableCell>
                  <TableCell></TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
              </TableRow>
            </TableFooter>
          </Table>
        </div>

        <div className="bg-background border rounded p-4 col-span-4">
          <h2>板卡通道</h2>
          <CardFooter className="flex flex-row items-center justify-between space-x-2">
          </CardFooter>
        </Card>
      </div>

    </div>
  )
}
