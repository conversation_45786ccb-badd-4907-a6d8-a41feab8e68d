@import "tailwindcss";
@import "tw-animate-css";
@import "./styles/fonts.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: 'Geist', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono: 'Geist', ui-monospace, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --font-display: '<PERSON>eist', 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-sensor: var(--sensor);
  --color-device: var(--device);
  --color-connection-channel: var(--connection-channel);
  --color-target: var(--target);
  --color-input-variable: var(--input-variable);
  --color-output-variable: var(--output-variable);
  --color-function-library: var(--function-library);
  --color-formula: var(--formula);
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2472 0.0164 264.2136);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.2472 0.0164 264.2136);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2472 0.0164 264.2136);
  --primary: oklch(0.5494 0.2490 263.0477);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9640 0.0029 264.5419);
  --secondary-foreground: oklch(0.2472 0.0164 264.2136);
  --muted: oklch(0.9640 0.0029 264.5419);
  --muted-foreground: oklch(0.6492 0.0214 252.9548);
  --accent: oklch(0.9640 0.0029 264.5419);
  --accent-foreground: oklch(0.2472 0.0164 264.2136);
  --destructive: oklch(0.6419 0.2182 25.8804);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9256 0.0068 277.1579);
  --input: oklch(0.9640 0.0029 264.5419);
  --ring: oklch(0.5494 0.2490 263.0477);
  --chart-1: oklch(0.5494 0.2490 263.0477);
  --chart-2: oklch(0.4942 0.2284 295.5827);
  --chart-3: oklch(0.6687 0.2142 144.3848);
  --chart-4: oklch(0.7274 0.1877 51.7460);
  --chart-5: oklch(0.6419 0.2182 25.8804);
  --sidebar: oklch(0.9789 0.0029 264.5421);
  --sidebar-foreground: oklch(0.4606 0.0296 257.6795);
  --sidebar-primary: oklch(0.5494 0.2490 263.0477);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5494 0.2490 263.0477);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.9256 0.0068 277.1579);
  --sidebar-ring: oklch(0.5494 0.2490 263.0477);
  --radius: 0.275rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;

  --sensor: "#3B82F6";
  --device: "#10B981";
  --connection-channel: "#8B5CF6";
  --target: "#EF4444";
  --input-variable: "#F59E0B";
  --output-variable: "#EC4899";
  --function-library: "#06B6D4";
  --formula: "#84CC16";
}

.dark {
  --background: oklch(0.2059 0.0059 285.8710);
  --foreground: oklch(0.9256 0.0068 277.1579);
  --card: oklch(0.2565 0.0019 286.2686);
  --card-foreground: oklch(0.8473 0.0106 261.7867);
  --popover: oklch(0.2565 0.0019 286.2686);
  --popover-foreground: oklch(0.8473 0.0106 261.7867);
  --primary: oklch(0.6215 0.2039 261.9758);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2854 0.0018 286.2850);
  --secondary-foreground: oklch(0.9256 0.0068 277.1579);
  --muted: oklch(0.2854 0.0018 286.2850);
  --muted-foreground: oklch(0.6492 0.0214 252.9548);
  --accent: oklch(0.2854 0.0018 286.2850);
  --accent-foreground: oklch(0.9256 0.0068 277.1579);
  --destructive: oklch(0.6419 0.2182 25.8804);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3492 0.0034 286.2208);
  --input: oklch(0.2854 0.0018 286.2850);
  --ring: oklch(0.6215 0.2039 261.9758);
  --chart-1: oklch(0.6215 0.2039 261.9758);
  --chart-2: oklch(0.5681 0.2051 300.6881);
  --chart-3: oklch(0.6687 0.2142 144.3848);
  --chart-4: oklch(0.7274 0.1877 51.7460);
  --chart-5: oklch(0.6419 0.2182 25.8804);
  --sidebar: oklch(0.2565 0.0019 286.2686);
  --sidebar-foreground: oklch(0.8473 0.0106 261.7867);
  --sidebar-primary: oklch(0.6215 0.2039 261.9758);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6215 0.2039 261.9758);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3492 0.0034 286.2208);
  --sidebar-ring: oklch(0.6215 0.2039 261.9758);

  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);

  --sensor: "#3B82F6";
  --device: "#10B981";
  --connection-channel: "#8B5CF6";
  --target: "#EF4444";
  --input-variable: "#F59E0B";
  --output-variable: "#EC4899";
  --function-library: "#06B6D4";
  --formula: "#84CC16";
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
